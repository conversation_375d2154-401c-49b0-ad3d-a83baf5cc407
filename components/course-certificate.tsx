'use client'

import { useState } from 'react'
import { Award, Download, Share2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import axios from 'axios'
import toast from 'react-hot-toast'

interface CourseCertificateProps {
  courseId: string
  courseName: string
  isCompleted: boolean
  completionDate?: Date
}

interface CertificateData {
  id: string
  studentName: string
  courseName: string
  completionDate: string
  certificateNumber: string
}

export function CourseCertificate({ 
  courseId, 
  courseName, 
  isCompleted, 
  completionDate 
}: CourseCertificateProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [certificate, setCertificate] = useState<CertificateData | null>(null)
  const [showCertificate, setShowCertificate] = useState(false)

  const generateCertificate = async () => {
    try {
      setIsLoading(true)
      
      // First get the certificate record
      const certResponse = await axios.get(`/api/courses/${courseId}/certificate`)
      
      // Then generate the PDF data
      const pdfResponse = await axios.post(`/api/courses/${courseId}/certificate`)
      
      setCertificate(pdfResponse.data)
      setShowCertificate(true)
      toast.success('Sertifikat berhasil dibuat!')
    } catch (error: any) {
      toast.error(error.response?.data || 'Gagal membuat sertifikat')
    } finally {
      setIsLoading(false)
    }
  }

  const downloadCertificate = () => {
    if (!certificate) return
    
    // For now, we'll create a simple HTML certificate that can be printed
    const certificateHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Sertifikat Penyelesaian</title>
        <style>
          body {
            font-family: 'Times New Roman', serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .certificate {
            background: white;
            padding: 60px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
            border: 8px solid #f0f0f0;
          }
          .header {
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
            margin-bottom: 40px;
          }
          .title {
            font-size: 48px;
            color: #667eea;
            margin: 0;
            font-weight: bold;
          }
          .subtitle {
            font-size: 18px;
            color: #666;
            margin: 10px 0 0 0;
          }
          .content {
            margin: 40px 0;
          }
          .student-name {
            font-size: 36px;
            color: #333;
            margin: 20px 0;
            font-weight: bold;
            border-bottom: 2px solid #667eea;
            display: inline-block;
            padding-bottom: 10px;
          }
          .course-name {
            font-size: 24px;
            color: #667eea;
            margin: 20px 0;
            font-style: italic;
          }
          .completion-text {
            font-size: 18px;
            color: #666;
            margin: 20px 0;
          }
          .footer {
            margin-top: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .date {
            font-size: 16px;
            color: #666;
          }
          .certificate-number {
            font-size: 14px;
            color: #999;
          }
          .seal {
            width: 80px;
            height: 80px;
            border: 3px solid #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #667eea;
            font-weight: bold;
          }
          @media print {
            body { background: white; }
            .certificate { box-shadow: none; }
          }
        </style>
      </head>
      <body>
        <div class="certificate">
          <div class="header">
            <h1 class="title">SERTIFIKAT</h1>
            <p class="subtitle">Penyelesaian Kursus</p>
          </div>
          
          <div class="content">
            <p class="completion-text">Dengan ini menyatakan bahwa</p>
            <div class="student-name">${certificate.studentName}</div>
            <p class="completion-text">telah berhasil menyelesaikan kursus</p>
            <div class="course-name">"${certificate.courseName}"</div>
            <p class="completion-text">pada tanggal ${certificate.completionDate}</p>
          </div>
          
          <div class="footer">
            <div>
              <div class="date">Tanggal: ${certificate.completionDate}</div>
              <div class="certificate-number">No. Sertifikat: ${certificate.certificateNumber}</div>
            </div>
            <div class="seal">
              VERIFIED
            </div>
          </div>
        </div>
      </body>
      </html>
    `
    
    const blob = new Blob([certificateHTML], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `sertifikat-${certificate.certificateNumber}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('Sertifikat berhasil diunduh! Buka file dan print untuk mendapatkan sertifikat PDF.')
  }

  const shareCertificate = () => {
    if (!certificate) return
    
    const shareText = `Saya telah menyelesaikan kursus "${certificate.courseName}" dan mendapatkan sertifikat! 🎓`
    
    if (navigator.share) {
      navigator.share({
        title: 'Sertifikat Penyelesaian Kursus',
        text: shareText,
      })
    } else {
      navigator.clipboard.writeText(shareText)
      toast.success('Teks berhasil disalin ke clipboard!')
    }
  }

  if (!isCompleted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Sertifikat Penyelesaian
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Award className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">
              Selesaikan semua bab untuk mendapatkan sertifikat
            </p>
            <Badge variant="secondary">Belum Selesai</Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Sertifikat Penyelesaian
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center">
            <Award className="h-16 w-16 text-yellow-500" />
          </div>
          
          <div>
            <h3 className="font-semibold text-lg">Selamat! 🎉</h3>
            <p className="text-gray-600">
              Anda telah menyelesaikan kursus ini
            </p>
            {completionDate && (
              <p className="text-sm text-gray-500 mt-1">
                Diselesaikan pada: {completionDate.toLocaleDateString('id-ID')}
              </p>
            )}
          </div>

          {!showCertificate ? (
            <Button 
              onClick={generateCertificate} 
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Membuat Sertifikat...' : 'Dapatkan Sertifikat'}
            </Button>
          ) : (
            <div className="space-y-3">
              <Badge variant="default" className="bg-green-100 text-green-800">
                Sertifikat Siap!
              </Badge>
              
              {certificate && (
                <div className="bg-gray-50 p-4 rounded-lg text-left">
                  <h4 className="font-medium mb-2">Detail Sertifikat:</h4>
                  <div className="text-sm space-y-1">
                    <p><strong>Nama:</strong> {certificate.studentName}</p>
                    <p><strong>Kursus:</strong> {certificate.courseName}</p>
                    <p><strong>Tanggal:</strong> {certificate.completionDate}</p>
                    <p><strong>No. Sertifikat:</strong> {certificate.certificateNumber}</p>
                  </div>
                </div>
              )}
              
              <div className="flex gap-2">
                <Button onClick={downloadCertificate} className="flex-1">
                  <Download className="h-4 w-4 mr-2" />
                  Unduh
                </Button>
                <Button onClick={shareCertificate} variant="outline" className="flex-1">
                  <Share2 className="h-4 w-4 mr-2" />
                  Bagikan
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
