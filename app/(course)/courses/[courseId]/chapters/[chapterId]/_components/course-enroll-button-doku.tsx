'use client'

import axios from 'axios'
import { useState } from 'react'
import toast from 'react-hot-toast'
import { useTranslations, useLocale } from 'next-intl'

import { Button } from '@/components/ui/button'
import { formatPrice } from '@/lib/format'

interface CourseEnrollButtonDokuProps {
  price: number
  courseId: string
}

export default function CourseEnrollButtonDoku({ price, courseId }: CourseEnrollButtonDokuProps) {
  const [isLoading, setIsLoading] = useState(false)
  const t = useTranslations('course')
  const locale = useLocale()

  const onClick = async () => {
    try {
      setIsLoading(true)
      const response = await axios.post(`/api/courses/${courseId}/doku-checkout`)
      
      // Show payment instructions to user
      const { virtualAccountNumber, amount } = response.data

      toast.success(
        `Payment created! Virtual Account: ${virtualAccountNumber}. Amount: ${formatPrice(amount)}`,
        { duration: 10000 }
      )

      // You could also redirect to a payment instruction page
      // window.location.assign(`/payment-instructions?va=${virtualAccountNumber}&amount=${amount}`)

    } catch {
      toast.error('Something went wrong!')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button className="w-full md:w-auto" size="sm" onClick={onClick} disabled={isLoading}>
      {t('enrollFor')} {formatPrice(price)}
    </Button>
  )
}
