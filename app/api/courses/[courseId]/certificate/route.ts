import { auth } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

type Params = Promise<{
  courseId: string
}>

// GET - Generate or retrieve certificate
export async function GET(req: NextRequest, { params }: { params: Params }) {
  try {
    const { userId } = await auth()
    const { courseId } = await params

    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Check if user has completed the course
    const course = await db.course.findUnique({
      where: { id: courseId },
      include: {
        chapters: { where: { isPublished: true } },
        purchases: { where: { userId } },
      },
    })

    if (!course) {
      return new NextResponse('Course not found', { status: 404 })
    }

    // Check if user has access to the course
    const hasAccess = course.isFree || course.purchases.length > 0

    if (!hasAccess) {
      return new NextResponse('You must purchase this course to get a certificate', { status: 403 })
    }

    // Check completion status
    const totalChapters = course.chapters.length
    const completedChapters = await db.userProgress.count({
      where: {
        userId,
        chapterId: { in: course.chapters.map(c => c.id) },
        isCompleted: true,
      },
    })

    if (completedChapters < totalChapters) {
      return new NextResponse('You must complete all chapters to get a certificate', { status: 403 })
    }

    // Check if certificate already exists
    let certificate = await db.certificate.findUnique({
      where: { userId_courseId: { userId, courseId } },
    })

    if (!certificate) {
      // Create new certificate
      certificate = await db.certificate.create({
        data: {
          userId,
          courseId,
          issuedAt: new Date(),
        },
      })
    }

    return NextResponse.json(certificate)
  } catch (error) {
    console.error('Error generating certificate:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}

// POST - Generate PDF certificate
export async function POST(req: NextRequest, { params }: { params: Params }) {
  try {
    const { userId } = await auth()
    const { courseId } = await params

    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Get certificate data
    const certificate = await db.certificate.findUnique({
      where: { userId_courseId: { userId, courseId } },
      include: {
        course: true,
      },
    })

    if (!certificate) {
      return new NextResponse('Certificate not found', { status: 404 })
    }

    // For now, return certificate data - PDF generation will be added later
    const certificateData = {
      id: certificate.id,
      studentName: 'Student', // We'll get this from Clerk user data later
      courseName: certificate.course.title,
      completionDate: certificate.issuedAt.toLocaleDateString('id-ID'),
      certificateNumber: `CERT-${certificate.id.slice(-8).toUpperCase()}`,
    }

    return NextResponse.json(certificateData)
  } catch (error) {
    console.error('Error generating PDF certificate:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}
