import { redirect } from 'next/navigation'
import { CheckCircle, Clock, Award, TrendingUp } from 'lucide-react'
import { auth } from '@clerk/nextjs/server'
import CoursesList from '@/components/course-list'
import { getDashboardCourses } from '@/actions/get-dashboard-courses'
import { InfoCard } from './_components/info-card'
import { StudentAchievements } from './_components/student-achievements'
import { RecentCertificates } from './_components/recent-certificates'
import { CourseRecommendations } from './_components/course-recommendations'

export default async function Dashboard() {
  const { userId } = await auth()

  if (!userId) {
    return redirect('/')
  }

  const { completedCourses, coursesInProgress } = await getDashboardCourses(userId)

  return (
    <div className="space-y-6 p-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <InfoCard
          icon={Clock}
          label="Sedang Dipelajari"
          numberOfItems={coursesInProgress.length}
        />
        <InfoCard
          icon={CheckCircle}
          label="Selesai"
          numberOfItems={completedCourses.length}
          variant="success"
        />
        <InfoCard
          icon={Award}
          label="Sertifikat"
          numberOfItems={completedCourses.length}
          variant="default"
        />
        <InfoCard
          icon={TrendingUp}
          label="Progress Rata-rata"
          numberOfItems={Math.round(
            [...coursesInProgress, ...completedCourses].reduce((acc, course) => acc + course.progress, 0) /
            ([...coursesInProgress, ...completedCourses].length || 1)
          )}
          format="percentage"
        />
      </div>

      {/* Achievements Section - Show if user has any courses */}
      {(completedCourses.length > 0 || coursesInProgress.length > 0) && (
        <StudentAchievements
          completedCourses={completedCourses}
          coursesInProgress={coursesInProgress}
        />
      )}

      {/* Recent Certificates - Only show if user has completed courses */}
      {completedCourses.length > 0 && (
        <RecentCertificates
          completedCourses={completedCourses.slice(0, 3)}
        />
      )}

      {/* Course Lists */}
      <div className="space-y-6">
        {coursesInProgress.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold mb-4">Lanjutkan Belajar</h2>
            <CoursesList items={coursesInProgress} />
          </div>
        )}

        {completedCourses.length > 0 && (
          <div>
            <h2 className="text-2xl font-bold mb-4">Kursus yang Telah Selesai</h2>
            <CoursesList items={completedCourses} showCertificateButton />
          </div>
        )}
      </div>

      {/* Course Recommendations - Always show */}
      <CourseRecommendations
        completedCourses={completedCourses}
        userId={userId}
      />
    </div>
  )
}
