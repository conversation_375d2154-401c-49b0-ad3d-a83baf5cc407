declare module 'doku-nodejs-library' {
  export class Snap {
    constructor(config: {
      isProduction: boolean
      clientId?: string
      secretKey?: string
      privateKey?: string
      publicKey?: string
    })

    createVa(params: any): Promise<any>
    checkStatusVa(params: any): Promise<any>

    createPayment(params: {
      orderId: string
      amount: number
      customerName: string
      customerEmail: string
      description: string
      successUrl: string
      cancelUrl: string
    }): Promise<{
      paymentUrl: string
      orderId: string
      virtualAccountNumber?: string
    }>

    verifySignature(signature: string, body: string): boolean
  }
}
