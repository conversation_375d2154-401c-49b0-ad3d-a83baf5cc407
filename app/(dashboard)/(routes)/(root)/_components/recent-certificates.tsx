'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Award, Eye } from 'lucide-react'
import Link from 'next/link'

interface Course {
  id: string
  title: string
  progress: number
  imageUrl?: string | null
}

interface RecentCertificatesProps {
  completedCourses: Course[]
}

export function RecentCertificates({ completedCourses }: RecentCertificatesProps) {

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Sertifikat Terbaru
        </CardTitle>
      </CardHeader>
      <CardContent>
        {completedCourses.length === 0 ? (
          <div className="text-center py-8">
            <Award className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-600 mb-2">
              Belum ada sertifikat
            </p>
            <p className="text-sm text-gray-500">
              Selesaikan kursus untuk mendapatkan sertifikat
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {completedCourses.map((course) => (
              <div
                key={course.id}
                className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                {/* Course Image */}
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center flex-shrink-0">
                  {course.imageUrl ? (
                    <img
                      src={course.imageUrl}
                      alt={course.title}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Award className="h-8 w-8 text-blue-600" />
                  )}
                </div>

                {/* Course Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm truncate">{course.title}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <Award className="h-3 w-3 mr-1" />
                      Sertifikat Tersedia
                    </Badge>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Kursus diselesaikan dengan progress 100%
                  </p>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  <Link href={`/courses/${course.id}/complete`}>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4 mr-1" />
                      Lihat
                    </Button>
                  </Link>
                </div>
              </div>
            ))}

            {/* View All Certificates Button */}
            {completedCourses.length > 0 && (
              <div className="text-center pt-4 border-t">
                <Link href="/dashboard/certificates">
                  <Button variant="outline" className="w-full">
                    <Award className="h-4 w-4 mr-2" />
                    Lihat Semua Sertifikat ({completedCourses.length})
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
