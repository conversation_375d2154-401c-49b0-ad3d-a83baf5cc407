import { auth } from '@clerk/nextjs/server'
import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

type Params = Promise<{
  courseId: string
}>

// GET - Fetch all reviews for a course
export async function GET(req: NextRequest, { params }: { params: Params }) {
  try {
    const { courseId } = await params

    const reviews = await db.review.findMany({
      where: { courseId },
      orderBy: { createdAt: 'desc' },
    })

    return NextResponse.json(reviews)
  } catch (error) {
    console.error('Error fetching reviews:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}

// POST - Create a new review
export async function POST(req: NextRequest, { params }: { params: Params }) {
  try {
    const { userId } = await auth()
    const { courseId } = await params
    const { rating, comment } = await req.json()

    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    // Check if user has purchased the course or it's free
    const course = await db.course.findUnique({
      where: { id: courseId },
      include: {
        purchases: { where: { userId } },
      },
    })

    if (!course) {
      return new NextResponse('Course not found', { status: 404 })
    }

    // Check if user has access to the course
    const hasAccess = course.isFree || course.purchases.length > 0

    if (!hasAccess) {
      return new NextResponse('You must purchase this course to leave a review', { status: 403 })
    }

    // Check if user has completed the course
    const totalChapters = await db.chapter.count({
      where: { courseId, isPublished: true },
    })

    const completedChapters = await db.userProgress.count({
      where: {
        userId,
        chapterId: { in: await db.chapter.findMany({ where: { courseId, isPublished: true }, select: { id: true } }).then(chapters => chapters.map(c => c.id)) },
        isCompleted: true,
      },
    })

    if (completedChapters < totalChapters) {
      return new NextResponse('You must complete the course to leave a review', { status: 403 })
    }

    // Create or update review
    const review = await db.review.upsert({
      where: { userId_courseId: { userId, courseId } },
      update: { rating, comment },
      create: { userId, courseId, rating, comment },
    })

    // Update course analytics
    await updateCourseAnalytics(courseId)

    return NextResponse.json(review)
  } catch (error) {
    console.error('Error creating review:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
}

async function updateCourseAnalytics(courseId: string) {
  try {
    // Calculate average rating
    const reviews = await db.review.findMany({
      where: { courseId },
      select: { rating: true },
    })

    const averageRating = reviews.length > 0 
      ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
      : null

    // Get other analytics data
    const totalEnrollments = await db.purchase.count({
      where: { courseId },
    }) + await db.course.count({
      where: { id: courseId, isFree: true },
    })

    const totalCompletions = await db.userProgress.groupBy({
      by: ['userId'],
      where: {
        chapter: { courseId },
        isCompleted: true,
      },
      _count: { userId: true },
    })

    const course = await db.course.findUnique({
      where: { id: courseId },
      include: { chapters: { where: { isPublished: true } } },
    })

    const completionCount = totalCompletions.filter(
      completion => completion._count.userId === course?.chapters.length
    ).length

    const completionRate = totalEnrollments > 0 ? (completionCount / totalEnrollments) * 100 : 0

    // Update or create analytics
    await db.courseAnalytics.upsert({
      where: { courseId },
      update: {
        averageRating,
        totalEnrollments,
        totalCompletions: completionCount,
        completionRate,
      },
      create: {
        courseId,
        averageRating,
        totalEnrollments,
        totalCompletions: completionCount,
        completionRate,
      },
    })
  } catch (error) {
    console.error('Error updating course analytics:', error)
  }
}
