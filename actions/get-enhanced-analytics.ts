import { db } from '@/lib/db'

export interface EnhancedAnalytics {
  // Basic metrics
  totalRevenue: number
  totalSales: number
  totalStudents: number
  totalCourses: number

  // Course performance
  coursePerformance: {
    courseId: string
    title: string
    enrollments: number
    completions: number
    completionRate: number
    averageRating: number | null
    revenue: number
    totalReviews: number
  }[]

  // Student engagement
  studentEngagement: {
    totalActiveStudents: number
    averageProgressPerStudent: number
    studentsCompletedCourses: number
  }

  // Revenue trends (last 12 months)
  revenueTrends: {
    month: string
    revenue: number
    sales: number
  }[]

  // Top performing courses
  topCourses: {
    courseId: string
    title: string
    metric: number
    type: 'revenue' | 'enrollments' | 'rating'
  }[]

  // Recent activity
  recentActivity: {
    type: 'enrollment' | 'completion' | 'review'
    courseName: string
    date: Date
    details: string
  }[]
}

export async function getEnhancedAnalytics(userId: string): Promise<EnhancedAnalytics> {
  try {
    // Get teacher's courses
    const courses = await db.course.findMany({
      where: { createdById: userId },
      include: {
        purchases: true,
        reviews: true,
        chapters: { where: { isPublished: true } },
        courseAnalytics: true,
      },
    })

    // Basic metrics
    const totalRevenue = courses.reduce((sum, course) => {
      return sum + (course.purchases.length * (course.price || 0))
    }, 0)

    const totalSales = courses.reduce((sum, course) => sum + course.purchases.length, 0)
    
    const totalStudents = new Set(
      courses.flatMap(course => course.purchases.map(p => p.userId))
    ).size

    const totalCourses = courses.length

    // Course performance
    const coursePerformance = await Promise.all(
      courses.map(async (course) => {
        const enrollments = course.isFree ? 
          await db.userProgress.groupBy({
            by: ['userId'],
            where: { chapter: { courseId: course.id } },
          }).then(users => users.length) :
          course.purchases.length

        const totalChapters = course.chapters.length
        
        // Get completions
        const userCompletions = await db.userProgress.groupBy({
          by: ['userId'],
          where: {
            chapter: { courseId: course.id },
            isCompleted: true,
          },
          _count: { userId: true },
        })

        const completions = userCompletions.filter(
          completion => completion._count.userId === totalChapters
        ).length

        const completionRate = enrollments > 0 ? (completions / enrollments) * 100 : 0

        const averageRating = course.reviews.length > 0 ?
          course.reviews.reduce((sum, review) => sum + review.rating, 0) / course.reviews.length :
          null

        return {
          courseId: course.id,
          title: course.title,
          enrollments,
          completions,
          completionRate,
          averageRating,
          revenue: course.purchases.length * (course.price || 0),
          totalReviews: course.reviews.length,
        }
      })
    )

    // Student engagement
    const allUserProgress = await db.userProgress.findMany({
      where: { chapter: { course: { createdById: userId } } },
      include: { chapter: { include: { course: true } } },
    })

    const studentProgress = allUserProgress.reduce((acc, progress) => {
      const userId = progress.userId
      const courseId = progress.chapter.courseId
      
      if (!acc[userId]) acc[userId] = {}
      if (!acc[userId][courseId]) acc[userId][courseId] = { completed: 0, total: 0 }
      
      acc[userId][courseId].total++
      if (progress.isCompleted) acc[userId][courseId].completed++
      
      return acc
    }, {} as Record<string, Record<string, { completed: number; total: number }>>)

    const totalActiveStudents = Object.keys(studentProgress).length
    
    const averageProgressPerStudent = totalActiveStudents > 0 ?
      Object.values(studentProgress).reduce((sum, userCourses) => {
        const userAverage = Object.values(userCourses).reduce((courseSum, course) => {
          return courseSum + (course.completed / course.total)
        }, 0) / Object.keys(userCourses).length
        return sum + userAverage
      }, 0) / totalActiveStudents * 100 : 0

    const studentsCompletedCourses = Object.values(studentProgress).filter(userCourses => {
      return Object.values(userCourses).some(course => course.completed === course.total && course.total > 0)
    }).length

    // Revenue trends (simplified - last 6 months)
    const revenueTrends = []
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)

      const monthPurchases = await db.purchase.findMany({
        where: {
          course: { createdById: userId },
          createdAt: { gte: monthStart, lte: monthEnd },
        },
        include: { course: true },
      })

      const monthRevenue = monthPurchases.reduce((sum, purchase) => {
        return sum + (purchase.course.price || 0)
      }, 0)

      revenueTrends.push({
        month: date.toLocaleDateString('id-ID', { month: 'short', year: 'numeric' }),
        revenue: monthRevenue,
        sales: monthPurchases.length,
      })
    }

    // Top performing courses
    const topCoursesByRevenue = [...coursePerformance]
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 3)
      .map(course => ({
        courseId: course.courseId,
        title: course.title,
        metric: course.revenue,
        type: 'revenue' as const,
      }))

    const topCoursesByEnrollments = [...coursePerformance]
      .sort((a, b) => b.enrollments - a.enrollments)
      .slice(0, 3)
      .map(course => ({
        courseId: course.courseId,
        title: course.title,
        metric: course.enrollments,
        type: 'enrollments' as const,
      }))

    const topCoursesByRating = [...coursePerformance]
      .filter(course => course.averageRating !== null)
      .sort((a, b) => (b.averageRating || 0) - (a.averageRating || 0))
      .slice(0, 3)
      .map(course => ({
        courseId: course.courseId,
        title: course.title,
        metric: course.averageRating || 0,
        type: 'rating' as const,
      }))

    const topCourses = [...topCoursesByRevenue, ...topCoursesByEnrollments, ...topCoursesByRating]

    // Recent activity (last 10 activities)
    const recentPurchases = await db.purchase.findMany({
      where: { course: { createdById: userId } },
      include: { course: true },
      orderBy: { createdAt: 'desc' },
      take: 5,
    })

    const recentReviews = await db.review.findMany({
      where: { course: { createdById: userId } },
      include: { course: true },
      orderBy: { createdAt: 'desc' },
      take: 5,
    })

    const recentActivity = [
      ...recentPurchases.map(purchase => ({
        type: 'enrollment' as const,
        courseName: purchase.course.title,
        date: purchase.createdAt,
        details: 'Siswa baru mendaftar',
      })),
      ...recentReviews.map(review => ({
        type: 'review' as const,
        courseName: review.course.title,
        date: review.createdAt,
        details: `Rating ${review.rating} bintang`,
      })),
    ]
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, 10)

    return {
      totalRevenue,
      totalSales,
      totalStudents,
      totalCourses,
      coursePerformance,
      studentEngagement: {
        totalActiveStudents,
        averageProgressPerStudent,
        studentsCompletedCourses,
      },
      revenueTrends,
      topCourses,
      recentActivity,
    }
  } catch (error) {
    console.error('Error getting enhanced analytics:', error)
    return {
      totalRevenue: 0,
      totalSales: 0,
      totalStudents: 0,
      totalCourses: 0,
      coursePerformance: [],
      studentEngagement: {
        totalActiveStudents: 0,
        averageProgressPerStudent: 0,
        studentsCompletedCourses: 0,
      },
      revenueTrends: [],
      topCourses: [],
      recentActivity: [],
    }
  }
}
