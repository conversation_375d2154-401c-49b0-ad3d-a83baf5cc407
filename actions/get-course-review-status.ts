import { db } from '@/lib/db'

export async function getCourseReviewStatus(userId: string, courseId: string) {
  try {
    // Check if user has access to the course
    const course = await db.course.findUnique({
      where: { id: courseId },
      include: {
        purchases: { where: { userId } },
        chapters: { where: { isPublished: true } },
      },
    })

    if (!course) {
      return { canReview: false, userReview: null, hasAccess: false }
    }

    const hasAccess = course.isFree || course.purchases.length > 0

    if (!hasAccess) {
      return { canReview: false, userReview: null, hasAccess: false }
    }

    // Check if user has completed the course
    const totalChapters = course.chapters.length
    const completedChapters = await db.userProgress.count({
      where: {
        userId,
        chapterId: { in: course.chapters.map(c => c.id) },
        isCompleted: true,
      },
    })

    const isCompleted = completedChapters === totalChapters && totalChapters > 0

    // Get existing review
    const userReview = await db.review.findUnique({
      where: { userId_courseId: { userId, courseId } },
    })

    return {
      canReview: isCompleted,
      userReview,
      hasAccess: true,
      isCompleted,
    }
  } catch (error) {
    console.error('Error getting course review status:', error)
    return { canReview: false, userReview: null, hasAccess: false }
  }
}
