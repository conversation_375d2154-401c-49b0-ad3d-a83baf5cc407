'use client'

import { Button } from '@/components/ui/button'

interface ShareButtonProps {
  courseTitle: string
}

export function ShareButton({ courseTitle }: ShareButtonProps) {
  const handleShare = () => {
    const shareText = `<PERSON><PERSON> telah menye<PERSON>aikan kursus "${courseTitle}" dan mendapatkan sertifikat! 🎓`
    
    if (navigator.share) {
      navigator.share({ 
        title: 'Pencapaian Kursus', 
        text: shareText 
      })
    } else {
      navigator.clipboard.writeText(shareText)
    }
  }

  return (
    <Button 
      variant="outline" 
      size="sm"
      onClick={handleShare}
    >
      Bagikan
    </Button>
  )
}
