import { clerkClient } from '@clerk/nextjs/server'

export async function getUserInfo(userId: string) {
  try {
    const client = await clerkClient()
    const user = await client.users.getUser(userId)
    
    // Get user name (prefer firstName + lastName, fallback to username or email)
    const fullName = user.firstName && user.lastName 
      ? `${user.firstName} ${user.lastName}`
      : user.username || user.emailAddresses[0]?.emailAddress || 'User'

    return {
      id: user.id,
      fullName,
      firstName: user.firstName,
      lastName: user.lastName,
      username: user.username,
      email: user.emailAddresses[0]?.emailAddress,
      imageUrl: user.imageUrl,
    }
  } catch (error) {
    console.error('Error getting user info:', error)
    return {
      id: userId,
      fullName: 'User',
      firstName: null,
      lastName: null,
      username: null,
      email: null,
      imageUrl: null,
    }
  }
}
